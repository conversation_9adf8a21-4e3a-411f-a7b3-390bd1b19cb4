/* Mobile and Tablet Override Styles - Force Hide Elements */

/* Mobile devices (under 768px) */
@media screen and (max-width: 767px) {
  /* Force hide thumb elements with maximum specificity */
  .tp-about-2-thumb-main,
  .tp-about-2-thumb-inner,
  .tp-about-2-right-thumb,
  div.tp-about-2-thumb-main,
  div.tp-about-2-thumb-inner,
  div.tp-about-2-right-thumb,
  .tp-about-2-thumb-box .tp-about-2-thumb-main,
  .tp-about-2-thumb-box .tp-about-2-thumb-inner,
  .tp-about-2-right-thumb.text-end {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
  }
  
  /* Disable tp-video-area animations on mobile */
  .tp-video-area,
  .tp-video-area * {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* Tablet devices (768px to 1024px) */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  /* Force hide thumb elements with maximum specificity */
  .tp-about-2-thumb-main,
  .tp-about-2-thumb-inner,
  .tp-about-2-right-thumb,
  div.tp-about-2-thumb-main,
  div.tp-about-2-thumb-inner,
  div.tp-about-2-right-thumb,
  .tp-about-2-thumb-box .tp-about-2-thumb-main,
  .tp-about-2-thumb-box .tp-about-2-thumb-inner,
  .tp-about-2-right-thumb.text-end {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
  }
  
  /* Disable tp-video-area animations on tablets */
  .tp-video-area,
  .tp-video-area * {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* Additional fallback for any screen under 1025px */
@media screen and (max-width: 1024px) {
  .tp-about-2-thumb-main,
  .tp-about-2-thumb-inner,
  .tp-about-2-right-thumb {
    display: none !important;
  }
}

/* Debug: Add red border to parent container to verify CSS is loading */
@media screen and (max-width: 1024px) {
  .tp-about-2-thumb-box {
    border: 2px solid red !important;
  }

  /* Add content to show CSS is working */
  .tp-about-2-thumb-box::before {
    content: "MOBILE/TABLET MODE - THUMBS HIDDEN" !important;
    display: block !important;
    background: red !important;
    color: white !important;
    padding: 10px !important;
    text-align: center !important;
    font-weight: bold !important;
    margin-bottom: 10px !important;
  }
}
